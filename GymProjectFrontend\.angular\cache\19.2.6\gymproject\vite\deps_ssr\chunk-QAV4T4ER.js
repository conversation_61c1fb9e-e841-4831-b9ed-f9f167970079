import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  Mat<PERSON><PERSON><PERSON>,
  MatFormField,
  MatHint,
  MatL<PERSON>l,
  MatPrefix,
  MatSuffix
} from "./chunk-S7BU6ENL.js";
import {
  MatCommonModule,
  ObserversModule
} from "./chunk-SSFRF6EM.js";
import {
  NgModule,
  setClassMetadata,
  ɵɵdefineInjector,
  ɵɵdefineNgModule
} from "./chunk-HGNYHDJR.js";
import {
  require_cjs
} from "./chunk-EXQLYBKH.js";
import {
  require_operators
} from "./chunk-HGVHWTGE.js";
import {
  __toESM
} from "./chunk-GBTWTWDP.js";

// node_modules/@angular/cdk/fesm2022/observers.mjs
var import_rxjs = __toESM(require_cjs(), 1);
var import_operators = __toESM(require_operators(), 1);

// node_modules/@angular/material/fesm2022/module-3bb03da5.mjs
var MatFormFieldModule = class _MatFormFieldModule {
  static ɵfac = function MatFormFieldModule_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _MatFormFieldModule)();
  };
  static ɵmod = ɵɵdefineNgModule({
    type: _MatFormFieldModule,
    imports: [MatCommonModule, ObserversModule, MatFormField, MatLabel, MatError, MatHint, MatPrefix, MatSuffix],
    exports: [MatFormField, MatLabel, MatHint, MatError, MatPrefix, MatSuffix, MatCommonModule]
  });
  static ɵinj = ɵɵdefineInjector({
    imports: [MatCommonModule, ObserversModule, MatCommonModule]
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatFormFieldModule, [{
    type: NgModule,
    args: [{
      imports: [MatCommonModule, ObserversModule, MatFormField, MatLabel, MatError, MatHint, MatPrefix, MatSuffix],
      exports: [MatFormField, MatLabel, MatHint, MatError, MatPrefix, MatSuffix, MatCommonModule]
    }]
  }], null, null);
})();

export {
  MatFormFieldModule
};
//# sourceMappingURL=chunk-QAV4T4ER.js.map
