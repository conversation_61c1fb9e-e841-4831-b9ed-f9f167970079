/* Company User Detail Dialog Styles */

/* Dialog Container */
.dialog-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--card-bg);
  color: var(--text-primary);
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
}

.spinner-container {
  text-align: center;
  color: var(--text-primary);
}

.content-blur {
  filter: blur(3px);
  pointer-events: none;
}

/* Dialog Header */
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--card-bg);
}

.user-header-info {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.avatar-circle-xl {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 32px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.user-header-details {
  flex: 1;
}

.user-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.user-email {
  color: var(--text-secondary);
  font-size: 1rem;
  margin-bottom: 0.75rem;
}

.btn-close {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background: var(--danger-light);
  color: var(--danger-dark);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-close:hover {
  background: var(--danger);
  color: white;
  transform: scale(1.1);
}

/* Tab Navigation */
.tab-navigation {
  display: flex;
  background: var(--card-bg);
  border-bottom: 1px solid var(--border-color);
  padding: 0 2rem;
}

.tab-button {
  padding: 1rem 1.5rem;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
  position: relative;
}

.tab-button:hover {
  color: var(--primary);
  background: var(--hover-bg);
}

.tab-button.active {
  color: var(--primary);
  border-bottom-color: var(--primary);
  background: var(--primary-light);
}

/* Dialog Content */
.dialog-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background: var(--body-bg);
}

.tab-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Form Sections */
.form-section {
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  padding: 2rem;
  border: 1px solid var(--border-color);
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
}

.section-title i {
  color: var(--primary);
}

/* Form Controls */
.form-label {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.form-label.required::after {
  content: ' *';
  color: var(--danger);
}

.modern-input,
.modern-select,
.modern-textarea {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--input-bg);
  color: var(--text-primary);
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
  font-size: 0.95rem;
}

.modern-input:focus,
.modern-select:focus,
.modern-textarea:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
  background: var(--input-focus-bg);
  outline: none;
}

.modern-input:disabled,
.modern-select:disabled {
  background: var(--disabled-bg);
  color: var(--text-muted);
  cursor: not-allowed;
}

.modern-textarea {
  resize: vertical;
  min-height: 100px;
}

/* Validation Styles */
.is-invalid {
  border-color: var(--danger);
}

.invalid-feedback {
  color: var(--danger);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Statistics Tab */
.statistics-tab {
  padding: 0;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.stat-icon-members {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon-active {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon-revenue {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon-status {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content h3 {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.stat-content p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.9rem;
}

/* User Details Section */
.user-details-section {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 2rem;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border-color);
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 500;
  color: var(--text-secondary);
}

.detail-value {
  font-weight: 600;
  color: var(--text-primary);
}

/* Dialog Footer */
.dialog-footer {
  padding: 1.5rem 2rem;
  border-top: 1px solid var(--border-color);
  background: var(--card-bg);
  backdrop-filter: blur(10px);
}

.footer-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  align-items: center;
}

/* Modern Dialog Buttons */
.btn-dialog {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  font-size: 0.95rem;
  border-radius: 0.75rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-width: 120px;
  text-transform: none;
  letter-spacing: 0.025em;
}

.btn-dialog:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-dialog:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn-dialog:not(:disabled):active {
  transform: translateY(0);
  transition: transform 0.1s ease;
}

/* Cancel Button */
.btn-dialog-cancel {
  background: linear-gradient(135deg, var(--secondary) 0%, var(--secondary-dark) 100%);
  color: white;
  border: 2px solid transparent;
}

.btn-dialog-cancel:not(:disabled):hover {
  background: linear-gradient(135deg, var(--secondary-dark) 0%, #5a6c7d 100%);
  box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
}

/* Save Button */
.btn-dialog-save {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: white;
  border: 2px solid transparent;
  position: relative;
}

.btn-dialog-save:not(:disabled):hover {
  background: linear-gradient(135deg, var(--primary-dark) 0%, #3d52a0 100%);
  box-shadow: 0 8px 25px rgba(67, 97, 238, 0.4);
}

.btn-dialog-save:disabled {
  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
}

/* Custom Spinner */
.spinner-sm {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Company ID Badge in Header */
.company-id-badge {
  font-size: 0.85rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin-left: 0.5rem;
  opacity: 0.8;
}

/* Status Badges */
.status-active {
  background-color: var(--success-light);
  color: var(--success-dark);
}

.status-inactive {
  background-color: var(--danger-light);
  color: var(--danger-dark);
}

.status-user-inactive {
  background-color: var(--warning-light);
  color: var(--warning-dark);
}

.status-password-required {
  background-color: var(--info-light);
  color: var(--info-dark);
}

/* Responsive Design */
@media (max-width: 768px) {
  .dialog-header {
    padding: 1.5rem;
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .user-header-info {
    flex-direction: column;
    text-align: center;
  }
  
  .tab-navigation {
    padding: 0 1rem;
    flex-wrap: wrap;
  }
  
  .tab-button {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
  
  .dialog-content {
    padding: 1.5rem;
  }
  
  .form-section {
    padding: 1.5rem;
  }
  
  .statistics-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .stat-card {
    padding: 1.5rem;
  }
  
  .dialog-footer {
    padding: 1rem 1.5rem;
  }

  .footer-buttons {
    flex-direction: column;
    gap: 0.75rem;
  }

  .btn-dialog {
    width: 100%;
    min-width: auto;
  }

  .company-id-badge {
    display: block;
    margin-left: 0;
    margin-top: 0.25rem;
    font-size: 0.8rem;
  }
}

/* Dark Mode Compatibility */
[data-theme="dark"] .loading-overlay {
  background-color: rgba(0, 0, 0, 0.9);
}

[data-theme="dark"] .stat-card:hover {
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .btn-dialog-cancel {
  background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
}

[data-theme="dark"] .btn-dialog-cancel:not(:disabled):hover {
  background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
  box-shadow: 0 8px 25px rgba(74, 85, 104, 0.4);
}

[data-theme="dark"] .btn-dialog-save:not(:disabled):hover {
  box-shadow: 0 8px 25px rgba(67, 97, 238, 0.6);
}

[data-theme="dark"] .company-id-badge {
  color: var(--text-muted);
}
